"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Languages, Globe, Check, RotateCcw, X, Eye, FileText } from "lucide-react"

interface TranslatedVariable {
  variable_id: string
  translated_sentence: string
}

interface TranslateBlockPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  translatedBlock: any | null
  translatedVariables: TranslatedVariable[]
  sourceLanguage: string
  originalContext: string
  onReplace: () => void
  onRedo: () => void
  onCancel: () => void
}

export function TranslateBlockPreviewDialog({
  open,
  onOpenChange,
  translatedBlock,
  translatedVariables,
  sourceLanguage,
  originalContext,
  onReplace,
  onRedo,
  onCancel
}: TranslateBlockPreviewDialogProps) {
  const getLanguageDisplay = (langCode: string) => {
    const languageMap: Record<string, { display: string; color: string }> = {
      'es': { display: 'Espanyol', color: 'text-red-500' },
      'ca': { display: 'Català', color: 'text-yellow-500' },
      'fr': { display: 'Francès', color: 'text-blue-500' },
      'en': { display: 'Anglès', color: 'text-green-500' }
    }
    return languageMap[langCode] || { display: langCode.toUpperCase(), color: 'text-gray-500' }
  }

  const handleReplace = () => {
    onReplace()
    onOpenChange(false)
  }

  const handleRedo = () => {
    onRedo()
    onOpenChange(false)
  }

  const handleCancel = () => {
    onCancel()
    onOpenChange(false)
  }

  if (!translatedBlock) {
    return null
  }

  const { display: languageDisplay, color: languageColor } = getLanguageDisplay(sourceLanguage)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-600" />
            Previsualització de la Traducció del Bloc
          </DialogTitle>
          <DialogDescription>
            Revisa el contingut traduït del bloc abans d'aplicar els canvis.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Translation Info */}
          <div className="flex items-center gap-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2">
              <Globe className={`h-4 w-4 ${languageColor}`} />
              <span className="font-medium">Traduït des de: {languageDisplay}</span>
            </div>
            {originalContext && (
              <div className="flex-1">
                <Badge variant="outline" className="text-xs">
                  Context: {originalContext.length > 50 ? `${originalContext.substring(0, 50)}...` : originalContext}
                </Badge>
              </div>
            )}
          </div>

          {/* Block Preview */}
          <ScrollArea className="h-[400px] w-full border rounded-lg">
            <div className="p-4 space-y-4">
              {/* Block Info */}
              <div>
                <h3 className="text-lg font-semibold mb-2">{translatedBlock.name}</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  {translatedBlock.description || 'Sense descripció'}
                </p>
              </div>

              <Separator />

              {/* Translated Variables */}
              {translatedVariables.length > 0 && (
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <span>Variables Traduïdes</span>
                    <Badge variant="secondary" className="text-xs">
                      {translatedVariables.length}
                    </Badge>
                  </h4>
                  <div className="space-y-3">
                    {translatedVariables.map((translatedVar, index) => (
                      <div key={translatedVar.variable_id} className="p-3 border rounded-lg bg-green-50">
                        <div className="flex items-start gap-2">
                          <Badge variant="outline" className="text-xs shrink-0">
                            Variable {index + 1}
                          </Badge>
                          <div className="flex-1">
                            <div className="text-sm font-medium text-green-700 mb-1">
                              Contingut traduït:
                            </div>
                            <div className="text-sm text-gray-700 bg-white p-2 rounded border">
                              {translatedVar.translated_sentence}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Separator />

              {/* Block HTML Content */}
              {translatedBlock.html_content && (
                <div>
                  <h4 className="font-medium mb-3">Contingut HTML del Bloc</h4>
                  <div className="text-xs text-muted-foreground font-mono bg-gray-50 p-3 rounded max-h-40 overflow-y-auto border">
                    <pre className="whitespace-pre-wrap">{translatedBlock.html_content}</pre>
                  </div>
                </div>
              )}

              {/* Additional Block Info */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Visible:</span>
                  <Badge variant={translatedBlock.is_visible ? "default" : "secondary"} className="ml-2 text-xs">
                    {translatedBlock.is_visible ? "Sí" : "No"}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium">Posició:</span>
                  <span className="ml-2 text-muted-foreground">{translatedBlock.order_position}</span>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>

        <DialogFooter className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Cancel·lar
          </Button>
          
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleRedo}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Tornar a traduir
            </Button>
            <Button
              type="button"
              onClick={handleReplace}
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2"
            >
              <Check className="h-4 w-4" />
              Aplicar Traducció
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
