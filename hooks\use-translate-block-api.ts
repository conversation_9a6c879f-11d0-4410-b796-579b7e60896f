import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { NewsletterBlock, NewsletterBlockVariable } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface VariableSentence {
  variable_id: string
  variable_name: string
  sentence: string
}

interface TranslateBlockRequest {
  source_language: string
  block_id: string
  variable_sentences: VariableSentence[]
  context?: string
}

interface TranslateBlockResponse {
  block: NewsletterBlock
  translated_variables: {
    variable_id: string
    translated_sentence: string
  }[]
  source_language: string
  context?: string
}

interface UseTranslateBlockApiReturn {
  translateBlock: (request: TranslateBlockRequest) => Promise<TranslateBlockResponse>
  loading: boolean
  error: string | null
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useTranslateBlockApi(): UseTranslateBlockApiReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (session?.djangoAccessToken) {
      headers['Authorization'] = `Bearer ${session.djangoAccessToken}`
    }

    return headers
  }, [session?.djangoAccessToken])

  const translateBlock = useCallback(async (request: TranslateBlockRequest): Promise<TranslateBlockResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      // For testing purposes, simulate the API response
      // TODO: Replace with actual translation API endpoint when available
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000))

      // Simulate translation response
      const translatedResponse = simulateBlockTranslation(request)

      return translatedResponse

      // TODO: Uncomment and implement when actual translation API is available
      /*
      const response = await fetch(`${API_BASE_URL}/openai/traduce-block/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to translate block')
        throw new Error(errorMessage)
      }

      const data: TranslateBlockResponse = await response.json()
      return data
      */
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while translating block'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  return {
    translateBlock,
    loading,
    error
  }
}

// Helper function to extract variable sentences from a block for a specific language
export function extractVariableSentences(block: NewsletterBlock, sourceLanguage: string): VariableSentence[] {
  if (!block.variable_values || block.variable_values.length === 0) {
    return []
  }

  return block.variable_values.map(variable => ({
    variable_id: variable.id,
    variable_name: variable.name,
    sentence: variable.value[sourceLanguage as keyof typeof variable.value] || ''
  })).filter(item => item.sentence.trim() !== '') // Only include non-empty sentences
}

// Helper function to simulate block translation for testing
function simulateBlockTranslation(request: TranslateBlockRequest): TranslateBlockResponse {
  const languageMap: Record<string, string> = {
    'es': 'Español',
    'ca': 'Català', 
    'fr': 'Français',
    'en': 'English'
  }

  // Simulate translated variables
  const translatedVariables = request.variable_sentences.map(varSentence => ({
    variable_id: varSentence.variable_id,
    translated_sentence: `[TRANSLATED FROM ${request.source_language.toUpperCase()}] ${varSentence.sentence}`
  }))

  // Create a mock translated block (in real implementation, this would come from the API)
  const translatedBlock: NewsletterBlock = {
    id: request.block_id,
    name: `Translated Block (from ${languageMap[request.source_language] || request.source_language.toUpperCase()})`,
    description: `Block translated from ${request.source_language}`,
    html_content: `<!-- Block translated from ${request.source_language} with context: "${request.context || 'No context provided'}" -->\n<div>Translated content would appear here</div>`,
    order_position: 1,
    is_visible: true,
    variable_values: [], // This would be populated with translated variables
    last_edited_by: null,
    last_edited_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    element_type_display: 'Translated Block'
  }

  return {
    block: translatedBlock,
    translated_variables: translatedVariables,
    source_language: request.source_language,
    context: request.context
  }
}
